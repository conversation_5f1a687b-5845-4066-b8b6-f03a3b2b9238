import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Car, Truck, Globe, CheckCircle, Award, Clock, Users } from "lucide-react";
import heroImage from "@/assets/hero-cars.jpg";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Car className="h-8 w-8 text-primary" />
              <span className="text-2xl font-bold text-primary">조아무역</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#services" className="text-foreground hover:text-primary transition-colors">서비스</a>
              <a href="#features" className="text-foreground hover:text-primary transition-colors">특징</a>
              <a href="#contact" className="text-foreground hover:text-primary transition-colors">문의</a>
              <Button variant="hero" size="sm">
                <Phone className="h-4 w-4 mr-2" />
                빠른 상담
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${heroImage})` }}
        >
          <div className="absolute inset-0 bg-gradient-hero opacity-85"></div>
        </div>
        
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <div className="max-w-4xl mx-auto animate-fade-in">
            <Badge variant="outline" className="mb-6 bg-white/10 text-white border-white/20">
              전국 최고가 매입 보장
            </Badge>
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              중고차 매입의
              <br />
              <span className="text-yellow-300">새로운 기준</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-white/90 leading-relaxed">
              중고차, 중고차 수출, 폐차까지<br />
              믿을 수 있는 조아무역과 함께하세요
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button variant="hero" size="lg" className="text-lg px-8 py-4">
                <Phone className="h-5 w-5 mr-2" />
                무료 상담 받기
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-4 bg-white/10 text-white border-white/30 hover:bg-white/20">
                온라인 견적
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-8 mt-12 text-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 min-w-[120px]">
                <div className="text-2xl font-bold text-yellow-300">24시간</div>
                <div className="text-sm text-white/80">빠른 출장</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 min-w-[120px]">
                <div className="text-2xl font-bold text-yellow-300">전국</div>
                <div className="text-sm text-white/80">서비스 지역</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 min-w-[120px]">
                <div className="text-2xl font-bold text-yellow-300">최고가</div>
                <div className="text-sm text-white/80">매입 보장</div>
              </div>
            </div>
          </div>
        </div>

        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2"></div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-gradient-light">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">전문 서비스</Badge>
            <h2 className="text-4xl font-bold mb-4">조아무역의 전문 서비스</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              다양한 차량 매입 서비스로 고객의 만족을 최우선으로 합니다
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="p-8 text-center hover:shadow-medium transition-all duration-300 hover:-translate-y-2">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                  <Car className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-2xl font-semibold mb-4">중고차 매입</h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  국산차, 수입차 구분 없이 정확한 시세 조회로 합리적인 가격에 매입해드립니다. 
                  전문 감정사의 정밀한 평가를 통해 최고가 매입을 보장합니다.
                </p>
                <ul className="text-sm space-y-2 text-left">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-success mr-2" />
                    국산차/수입차 전문 매입
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-success mr-2" />
                    정확한 시세 기반 가격 책정
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-success mr-2" />
                    즉시 현금 결제 가능
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="p-8 text-center hover:shadow-medium transition-all duration-300 hover:-translate-y-2">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-gradient-secondary rounded-full flex items-center justify-center mx-auto mb-6">
                  <Globe className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-2xl font-semibold mb-4">중고차 수출</h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  해외 수출용 차량 매입으로 일반 매입보다 더 높은 가격을 제공합니다. 
                  러시아, 동남아시아 등 다양한 수출 루트를 보유하고 있습니다.
                </p>
                <ul className="text-sm space-y-2 text-left">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-success mr-2" />
                    다양한 해외 수출 루트 보유
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-success mr-2" />
                    일반 매입 대비 높은 가격
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-success mr-2" />
                    수출 관련 서류 대행
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="p-8 text-center hover:shadow-medium transition-all duration-300 hover:-translate-y-2">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                  <Truck className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-2xl font-semibold mb-4">폐차 매입</h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  노후 차량, 사고 차량까지 모든 차량을 매입합니다. 
                  환경친화적인 폐차 처리로 고객과 환경을 모두 생각합니다.
                </p>
                <ul className="text-sm space-y-2 text-left">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-success mr-2" />
                    노후/사고 차량 전문 매입
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-success mr-2" />
                    환경친화적 폐차 처리
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-success mr-2" />
                    폐차 관련 서류 무료 대행
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">조아무역만의 장점</Badge>
            <h2 className="text-4xl font-bold mb-4">왜 조아무역을 선택해야 할까요?</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              고객 만족을 위한 차별화된 서비스와 전문성으로 신뢰받는 파트너가 되겠습니다
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <Clock className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3">신속한 처리</h3>
              <p className="text-muted-foreground">
                24시간 언제든지 출장 가능하며, 현장에서 즉시 감정부터 계약까지 완료합니다.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-secondary rounded-full flex items-center justify-center mx-auto mb-6">
                <Award className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3">전문 감정</h3>
              <p className="text-muted-foreground">
                숙련된 전문 감정사의 정확한 평가로 차량의 진정한 가치를 인정받으세요.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3">투명한 거래</h3>
              <p className="text-muted-foreground">
                숨겨진 수수료 없이 투명하고 공정한 거래로 고객의 신뢰를 얻고 있습니다.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-secondary rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3">고객 만족</h3>
              <p className="text-muted-foreground">
                고객의 소중한 차량을 가족의 차량처럼 생각하며 최선의 서비스를 제공합니다.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gradient-light">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">연락처</Badge>
            <h2 className="text-4xl font-bold mb-4">지금 바로 문의하세요</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              전문 상담원이 친절하게 안내해드립니다. 24시간 언제든지 연락주세요.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <Card className="p-8 text-center hover:shadow-medium transition-all duration-300">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  <Phone className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">전화 상담</h3>
                <p className="text-2xl font-bold text-primary mb-2">010-1234-5678</p>
                <p className="text-sm text-muted-foreground">24시간 상담 가능</p>
              </CardContent>
            </Card>

            <Card className="p-8 text-center hover:shadow-medium transition-all duration-300">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                  <Mail className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">이메일 문의</h3>
                <p className="text-lg font-semibold text-primary mb-2"><EMAIL></p>
                <p className="text-sm text-muted-foreground">빠른 답변 보장</p>
              </CardContent>
            </Card>

            <Card className="p-8 text-center hover:shadow-medium transition-all duration-300">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">출장 지역</h3>
                <p className="text-lg font-semibold text-primary mb-2">전국 출장</p>
                <p className="text-sm text-muted-foreground">서울, 경기 우선 서비스</p>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-12">
            <Button size="lg" className="text-lg px-12 py-4">
              <Phone className="h-5 w-5 mr-2" />
              지금 무료 상담 받기
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-foreground text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Car className="h-8 w-8 text-primary-light" />
                <span className="text-2xl font-bold">조아무역</span>
              </div>
              <p className="text-white/80 mb-4">
                중고차, 중고차 수출, 폐차 매입 전문업체로서<br />
                고객의 신뢰와 만족을 최우선으로 하는 조아무역입니다.
              </p>
              <div className="flex space-x-4">
                <Badge variant="outline" className="bg-white/10 text-white border-white/20">
                  중고차 매입
                </Badge>
                <Badge variant="outline" className="bg-white/10 text-white border-white/20">
                  수출업
                </Badge>
                <Badge variant="outline" className="bg-white/10 text-white border-white/20">
                  폐차업
                </Badge>
              </div>
            </div>
            
            <div>
              <h4 className="text-xl font-semibold mb-4">연락처 정보</h4>
              <div className="space-y-3 text-white/80">
                <div className="flex items-center">
                  <Phone className="h-5 w-5 mr-3 text-primary-light" />
                  010-1234-5678 (24시간 상담)
                </div>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 mr-3 text-primary-light" />
                  <EMAIL>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-3 text-primary-light" />
                  전국 출장 서비스 (서울, 경기 우선)
                </div>
              </div>
            </div>
          </div>
          
          <div className="border-t border-white/20 mt-8 pt-8 text-center text-white/60">
            <p>&copy; 2024 조아무역. All rights reserved. 중고차 매입 전문업체</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;